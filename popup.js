document.addEventListener('DOMContentLoaded', function() {
  // 获取页面上的视频
  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    chrome.scripting.executeScript({
      target: {tabId: tabs[0].id},
      function: findVideos
    }, displayResults);
  });

  // 添加过滤器事件监听
  document.getElementById('resolution-filter').addEventListener('change', filterVideos);
  document.getElementById('format-filter').addEventListener('change', filterVideos);
});

// 在页面中查找视频
function findVideos() {
  // 查找所有网络请求
  const videos = [];

  // 从性能条目中获取资源
  const resources = performance.getEntriesByType('resource');

  // 筛选视频文件
  for (const resource of resources) {
    const url = resource.name;
    if (url.includes('pinimg.com/videos') &&
        (url.endsWith('.cmfv') || url.includes('.mp4'))) {

      // 提取分辨率信息
      let resolution = 'unknown';
      if (url.includes('240w')) resolution = '240w';
      else if (url.includes('360w')) resolution = '360w';
      else if (url.includes('720w')) resolution = '720w';

      // 提取格式
      let format = 'unknown';
      if (url.endsWith('.cmfv')) format = 'cmfv';
      else if (url.endsWith('.mp4')) format = 'mp4';

      videos.push({
        url: url,
        resolution: resolution,
        format: format,
        filename: url.split('/').pop()
      });
    }
  }

  return videos;
}

// 显示结果
function displayResults(results) {
  const videoList = document.getElementById('video-list');
  const statsElement = document.getElementById('stats');

  // 安全地检查结果
  if (!results || !results[0] || !results[0].result) {
    videoList.innerHTML = `
      <div class="no-videos">
        <div class="no-videos-icon">⚠️</div>
        <div class="no-videos-text">获取视频失败</div>
        <div class="no-videos-subtitle">请刷新页面后重试</div>
      </div>
    `;
    statsElement.style.display = 'none';
    return;
  }

  const videos = results[0].result;

  if (!videos || videos.length === 0) {
    videoList.innerHTML = `
      <div class="no-videos">
        <div class="no-videos-icon">🎬</div>
        <div class="no-videos-text">未找到视频</div>
        <div class="no-videos-subtitle">请确保页面已完全加载并包含视频内容</div>
      </div>
    `;
    statsElement.style.display = 'none';
    return;
  }

  // 保存全局视频列表用于过滤
  window.allVideos = videos;

  // 显示视频列表
  renderVideoList(videos);

  // 显示统计信息
  updateStats(videos.length);
}

// 渲染视频列表
function renderVideoList(videos) {
  const videoList = document.getElementById('video-list');
  videoList.innerHTML = '';

  videos.forEach((video, index) => {
    const videoItem = document.createElement('div');
    videoItem.className = 'video-item';

    // 添加延迟动画
    videoItem.style.animationDelay = `${index * 0.1}s`;

    // 创建视频缩略图
    const thumbnail = document.createElement('div');
    thumbnail.className = 'video-thumbnail';

    const thumbnailIcon = document.createElement('div');
    thumbnailIcon.className = 'video-thumbnail-icon';
    thumbnailIcon.textContent = '▶';

    thumbnail.appendChild(thumbnailIcon);

    // 创建视频信息容器
    const videoInfo = document.createElement('div');
    videoInfo.className = 'video-info';

    // 视频文件名（简化显示）
    const filename = document.createElement('div');
    filename.className = 'video-filename';
    const shortFilename = video.filename.length > 35
      ? video.filename.substring(0, 35) + '...'
      : video.filename;
    filename.textContent = shortFilename;
    filename.title = video.filename; // 完整文件名作为tooltip

    // 视频详情
    const details = document.createElement('div');
    details.className = 'video-details';

    const resolutionItem = document.createElement('div');
    resolutionItem.className = 'video-detail-item';
    resolutionItem.innerHTML = `<span>📺</span><span>${video.resolution}</span>`;

    const formatItem = document.createElement('div');
    formatItem.className = 'video-detail-item';
    formatItem.innerHTML = `<span>🎞️</span><span>${video.format.toUpperCase()}</span>`;

    details.appendChild(resolutionItem);
    details.appendChild(formatItem);

    videoInfo.appendChild(filename);
    videoInfo.appendChild(details);

    // 创建查看按钮
    const viewBtn = document.createElement('button');
    viewBtn.className = 'view-btn';
    viewBtn.innerHTML = '<span>▶</span><span>查看</span>';
    viewBtn.addEventListener('click', () => {
      // 添加点击反馈
      viewBtn.style.transform = 'scale(0.95)';
      setTimeout(() => {
        viewBtn.style.transform = '';
      }, 150);

      chrome.tabs.create({
        url: video.url,
        active: true
      });
    });

    // 将元素添加到视频项
    videoItem.appendChild(thumbnail);
    videoItem.appendChild(videoInfo);
    videoItem.appendChild(viewBtn);
    videoList.appendChild(videoItem);
  });
}

// 更新统计信息
function updateStats(count) {
  const statsElement = document.getElementById('stats');
  const countElement = document.getElementById('video-count');

  countElement.textContent = count;
  statsElement.style.display = count > 0 ? 'block' : 'none';
}

// 过滤视频
function filterVideos() {
  const resolutionFilter = document.getElementById('resolution-filter').value;
  const formatFilter = document.getElementById('format-filter').value;

  if (!window.allVideos) return;

  let filteredVideos = window.allVideos;

  if (resolutionFilter !== 'all') {
    filteredVideos = filteredVideos.filter(video => video.resolution === resolutionFilter);
  }

  if (formatFilter !== 'all') {
    filteredVideos = filteredVideos.filter(video => video.format === formatFilter);
  }

  renderVideoList(filteredVideos);
  updateStats(filteredVideos.length);
}
