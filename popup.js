document.addEventListener('DOMContentLoaded', function() {
  // 获取页面上的视频
  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    chrome.scripting.executeScript({
      target: {tabId: tabs[0].id},
      function: findVideos
    }, displayResults);
  });

  // 添加过滤器事件监听
  document.getElementById('resolution-filter').addEventListener('change', filterVideos);
  document.getElementById('format-filter').addEventListener('change', filterVideos);
});

// 在页面中查找视频
function findVideos() {
  // 查找所有网络请求
  const videos = [];

  // 从性能条目中获取资源
  const resources = performance.getEntriesByType('resource');

  // 筛选视频文件
  for (const resource of resources) {
    const url = resource.name;
    if (url.includes('pinimg.com/videos') &&
        (url.endsWith('.cmfv') || url.includes('.mp4'))) {

      // 提取分辨率信息
      let resolution = 'unknown';
      if (url.includes('240w')) resolution = '240w';
      else if (url.includes('360w')) resolution = '360w';
      else if (url.includes('720w')) resolution = '720w';

      // 提取格式
      let format = 'unknown';
      if (url.endsWith('.cmfv')) format = 'cmfv';
      else if (url.endsWith('.mp4')) format = 'mp4';

      videos.push({
        url: url,
        resolution: resolution,
        format: format,
        filename: url.split('/').pop()
      });
    }
  }

  return videos;
}

// 显示结果
function displayResults(results) {
  const videoList = document.getElementById('video-list');

  // 安全地检查结果
  if (!results || !results[0] || !results[0].result) {
    videoList.innerHTML = '<div class="no-videos">获取视频失败，请刷新页面后重试</div>';
    return;
  }

  const videos = results[0].result;

  if (!videos || videos.length === 0) {
    videoList.innerHTML = '<div class="no-videos">未找到视频</div>';
    return;
  }

  // 保存全局视频列表用于过滤
  window.allVideos = videos;

  // 显示视频列表
  renderVideoList(videos);
}

// 渲染视频列表
function renderVideoList(videos) {
  const videoList = document.getElementById('video-list');
  videoList.innerHTML = '';

  videos.forEach(video => {
    const videoItem = document.createElement('div');
    videoItem.className = 'video-item';

    const videoInfo = document.createElement('div');
    videoInfo.innerHTML = `
      <div>${video.filename}</div>
      <div>分辨率: ${video.resolution}, 格式: ${video.format}</div>
    `;

    // 创建按钮容器
    const buttonsContainer = document.createElement('div');
    buttonsContainer.className = 'buttons-container';

    // 创建查看按钮
    const viewBtn = document.createElement('button');
    viewBtn.className = 'view-btn';
    viewBtn.textContent = '查看';
    viewBtn.addEventListener('click', () => {
      chrome.tabs.create({
        url: video.url,
        active: true
      });
    });

    // 创建下载按钮
    const downloadBtn = document.createElement('button');
    downloadBtn.className = 'download-btn';
    downloadBtn.textContent = '下载';
    downloadBtn.addEventListener('click', () => {
      chrome.downloads.download({
        url: video.url,
        filename: video.filename
      });
    });

    // 将按钮添加到容器
    buttonsContainer.appendChild(viewBtn);
    buttonsContainer.appendChild(downloadBtn);

    // 将元素添加到视频项
    videoItem.appendChild(videoInfo);
    videoItem.appendChild(buttonsContainer);
    videoList.appendChild(videoItem);
  });
}

// 过滤视频
function filterVideos() {
  const resolutionFilter = document.getElementById('resolution-filter').value;
  const formatFilter = document.getElementById('format-filter').value;

  let filteredVideos = window.allVideos;

  if (resolutionFilter !== 'all') {
    filteredVideos = filteredVideos.filter(video => video.resolution === resolutionFilter);
  }

  if (formatFilter !== 'all') {
    filteredVideos = filteredVideos.filter(video => video.format === formatFilter);
  }

  renderVideoList(filteredVideos);
}
