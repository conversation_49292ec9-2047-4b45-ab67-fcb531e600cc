<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Pinterest 视频下载器</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      width: 420px;
      min-height: 500px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      color: #2c3e50;
      overflow: hidden;
    }

    .header {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      padding: 16px 20px;
      text-align: center;
      border-bottom: 1px solid rgba(0, 0, 0, 0.08);
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    .header h2 {
      font-size: 18px;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 12px;
      letter-spacing: -0.3px;
    }

    .filters {
      display: flex;
      gap: 12px;
      justify-content: center;
    }

    .filter-wrapper {
      position: relative;
    }

    .filters select {
      padding: 8px 12px 8px 32px;
      border: 1px solid #e1e8ed;
      border-radius: 8px;
      background: white;
      font-size: 13px;
      font-weight: 500;
      color: #2c3e50;
      cursor: pointer;
      transition: all 0.2s ease;
      outline: none;
      appearance: none;
      min-width: 120px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .filters select:hover {
      border-color: #3498db;
      box-shadow: 0 2px 6px rgba(52, 152, 219, 0.15);
    }

    .filters select:focus {
      border-color: #3498db;
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }

    .filter-icon {
      position: absolute;
      left: 10px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 14px;
      pointer-events: none;
      z-index: 1;
      opacity: 0.7;
    }

    .content {
      background: white;
      margin: 0;
      min-height: 400px;
    }

    .video-list {
      max-height: 450px;
      overflow-y: auto;
      padding: 4px 0;
    }

    .video-list::-webkit-scrollbar {
      width: 6px;
    }

    .video-list::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.05);
      border-radius: 3px;
    }

    .video-list::-webkit-scrollbar-thumb {
      background: #3498db;
      border-radius: 3px;
    }

    .video-list::-webkit-scrollbar-thumb:hover {
      background: #2980b9;
    }

    .video-item {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      margin: 2px 12px;
      border-radius: 8px;
      background: #fafbfc;
      border: 1px solid #e1e8ed;
      transition: all 0.2s ease;
      position: relative;
    }

    .video-item:hover {
      background: #f8f9fa;
      border-color: #3498db;
      box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
    }

    .video-thumbnail {
      width: 40px;
      height: 40px;
      border-radius: 6px;
      background: linear-gradient(135deg, #3498db, #2980b9);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      margin-right: 12px;
      flex-shrink: 0;
    }

    .video-thumbnail-icon {
      color: white;
      font-weight: bold;
    }

    .video-info {
      flex: 1;
      margin-right: 12px;
    }

    .video-filename {
      font-size: 14px;
      font-weight: 500;
      color: #2c3e50;
      margin-bottom: 4px;
      line-height: 1.3;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .video-details {
      display: flex;
      gap: 8px;
    }

    .video-detail-item {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 2px 6px;
      background: #ecf0f1;
      border-radius: 4px;
      font-size: 11px;
      font-weight: 500;
      color: #7f8c8d;
    }

    .video-detail-item span:first-child {
      font-size: 12px;
    }

    .view-btn {
      background: #27ae60;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 6px;
      font-size: 13px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      min-width: 70px;
      display: flex;
      align-items: center;
      gap: 6px;
      box-shadow: 0 1px 3px rgba(39, 174, 96, 0.3);
    }

    .view-btn:hover {
      background: #229954;
      transform: translateY(-1px);
      box-shadow: 0 2px 6px rgba(39, 174, 96, 0.4);
    }

    .view-btn:active {
      transform: translateY(0);
      box-shadow: 0 1px 3px rgba(39, 174, 96, 0.3);
    }

    .no-videos, .loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      text-align: center;
      background: #f8f9fa;
      margin: 16px;
      border-radius: 8px;
      border: 1px solid #e1e8ed;
    }

    .no-videos-icon, .loading-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.6;
    }

    .no-videos-text, .loading-text {
      font-size: 15px;
      font-weight: 500;
      color: #2c3e50;
      margin-bottom: 6px;
    }

    .no-videos-subtitle {
      font-size: 13px;
      color: #7f8c8d;
      opacity: 0.8;
    }

    .loading-spinner {
      width: 32px;
      height: 32px;
      border: 3px solid #e1e8ed;
      border-top: 3px solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 16px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .stats {
      padding: 8px 20px;
      background: #f8f9fa;
      border-top: 1px solid #e1e8ed;
      text-align: center;
      font-size: 12px;
      color: #7f8c8d;
      font-weight: 500;
    }
  </style>
</head>
<body>
  <div class="header">
    <h2>🎬 Pinterest 视频查看器</h2>
    <div class="filters">
      <div class="filter-wrapper">
        <div class="filter-icon">📺</div>
        <select id="resolution-filter">
          <option value="all">所有分辨率</option>
          <option value="240w">240p</option>
          <option value="360w">360p</option>
          <option value="720w">720p</option>
        </select>
      </div>

      <div class="filter-wrapper">
        <div class="filter-icon">🎞️</div>
        <select id="format-filter">
          <option value="all">所有格式</option>
          <option value="cmfv">CMFV</option>
          <option value="mp4">MP4</option>
        </select>
      </div>
    </div>
  </div>

  <div class="content">
    <div id="video-list" class="video-list">
      <div class="loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在搜索视频...</div>
      </div>
    </div>
    <div id="stats" class="stats" style="display: none;">
      找到 <span id="video-count">0</span> 个视频
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
