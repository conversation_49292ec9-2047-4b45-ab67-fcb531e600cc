<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Pinterest 视频下载器</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      width: 450px;
      min-height: 300px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #333;
    }

    .header {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      padding: 20px;
      text-align: center;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .header h2 {
      font-size: 18px;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 15px;
    }

    .filters {
      display: flex;
      gap: 12px;
      justify-content: center;
    }

    .filters select {
      padding: 8px 12px;
      border: 2px solid #e1e8ed;
      border-radius: 8px;
      background: white;
      font-size: 14px;
      color: #2c3e50;
      cursor: pointer;
      transition: all 0.3s ease;
      outline: none;
    }

    .filters select:hover {
      border-color: #4CAF50;
      box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
    }

    .filters select:focus {
      border-color: #4CAF50;
      box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
    }

    .content {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      margin: 0;
    }

    .video-list {
      max-height: 350px;
      overflow-y: auto;
      padding: 0;
    }

    .video-list::-webkit-scrollbar {
      width: 6px;
    }

    .video-list::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.1);
    }

    .video-list::-webkit-scrollbar-thumb {
      background: rgba(76, 175, 80, 0.5);
      border-radius: 3px;
    }

    .video-list::-webkit-scrollbar-thumb:hover {
      background: rgba(76, 175, 80, 0.7);
    }

    .video-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;
      background: white;
    }

    .video-item:hover {
      background: rgba(76, 175, 80, 0.05);
      transform: translateX(2px);
    }

    .video-item:last-child {
      border-bottom: none;
    }

    .video-info {
      flex: 1;
      margin-right: 15px;
    }

    .video-filename {
      font-size: 14px;
      font-weight: 500;
      color: #2c3e50;
      margin-bottom: 4px;
      word-break: break-all;
      line-height: 1.4;
    }

    .video-details {
      font-size: 12px;
      color: #7f8c8d;
      display: flex;
      gap: 12px;
    }

    .video-detail-item {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .view-btn {
      background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
      min-width: 80px;
    }

    .view-btn:hover {
      background: linear-gradient(135deg, #45a049 0%, #4CAF50 100%);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
    }

    .view-btn:active {
      transform: translateY(0);
      box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
    }

    .no-videos {
      color: #7f8c8d;
      text-align: center;
      padding: 40px 20px;
      font-size: 14px;
      background: white;
    }

    .no-videos-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    .loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      background: white;
    }

    .loading-spinner {
      width: 32px;
      height: 32px;
      border: 3px solid rgba(76, 175, 80, 0.2);
      border-top: 3px solid #4CAF50;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 16px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .loading-text {
      color: #7f8c8d;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div class="header">
    <h2>🎬 Pinterest 视频查看器</h2>
    <div class="filters">
      <select id="resolution-filter">
        <option value="all">📺 所有分辨率</option>
        <option value="240w">240p</option>
        <option value="360w">360p</option>
        <option value="720w">720p</option>
      </select>

      <select id="format-filter">
        <option value="all">🎞️ 所有格式</option>
        <option value="cmfv">CMFV</option>
        <option value="mp4">MP4</option>
      </select>
    </div>
  </div>

  <div class="content">
    <div id="video-list" class="video-list">
      <div class="loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在搜索视频...</div>
      </div>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
