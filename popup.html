<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Pinterest 视频下载器</title>
  <style>
    body {
      width: 400px;
      padding: 10px;
      font-family: Arial, sans-serif;
    }
    .filters {
      margin-bottom: 10px;
      display: flex;
      gap: 10px;
    }
    .video-list {
      max-height: 400px;
      overflow-y: auto;
    }
    .video-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px;
      border-bottom: 1px solid #eee;
    }
    .buttons-container {
      display: flex;
      gap: 5px;
    }
    .download-btn, .view-btn {
      border: none;
      padding: 5px 10px;
      border-radius: 4px;
      cursor: pointer;
    }
    .download-btn {
      background: #e60023;
      color: white;
    }
    .view-btn {
      background: #0074e4;
      color: white;
    }
    .no-videos {
      color: #666;
      text-align: center;
      padding: 20px;
    }
  </style>
</head>
<body>
  <h2>Pinterest 视频下载器</h2>
  
  <div class="filters">
    <select id="resolution-filter">
      <option value="all">所有分辨率</option>
      <option value="240w">240p</option>
      <option value="360w">360p</option>
      <option value="720w">720p</option>
    </select>
    
    <select id="format-filter">
      <option value="all">所有格式</option>
      <option value="cmfv">CMFV</option>
      <option value="mp4">MP4</option>
    </select>
  </div>
  
  <div id="video-list" class="video-list">
    <div class="no-videos">正在搜索视频...</div>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
