<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Pinterest 视频下载器</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      width: 480px;
      min-height: 400px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      background: linear-gradient(145deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
      color: #333;
      overflow: hidden;
    }

    .header {
      background: rgba(255, 255, 255, 0.98);
      backdrop-filter: blur(20px);
      padding: 24px 20px;
      text-align: center;
      border-bottom: 1px solid rgba(255, 255, 255, 0.3);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .header h2 {
      font-size: 20px;
      font-weight: 700;
      background: linear-gradient(135deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin-bottom: 18px;
      letter-spacing: -0.5px;
    }

    .filters {
      display: flex;
      gap: 16px;
      justify-content: center;
    }

    .filter-wrapper {
      position: relative;
    }

    .filters select {
      padding: 12px 16px 12px 40px;
      border: 2px solid rgba(102, 126, 234, 0.2);
      border-radius: 12px;
      background: rgba(255, 255, 255, 0.9);
      font-size: 14px;
      font-weight: 500;
      color: #2c3e50;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      outline: none;
      appearance: none;
      min-width: 140px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .filters select:hover {
      border-color: #667eea;
      background: white;
      transform: translateY(-1px);
      box-shadow: 0 4px 16px rgba(102, 126, 234, 0.2);
    }

    .filters select:focus {
      border-color: #667eea;
      background: white;
      box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    }

    .filter-icon {
      position: absolute;
      left: 12px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 16px;
      pointer-events: none;
      z-index: 1;
    }

    .content {
      background: rgba(255, 255, 255, 0.98);
      backdrop-filter: blur(20px);
      margin: 0;
      min-height: 300px;
    }

    .video-list {
      max-height: 400px;
      overflow-y: auto;
      padding: 8px 0;
    }

    .video-list::-webkit-scrollbar {
      width: 8px;
    }

    .video-list::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.05);
      border-radius: 4px;
    }

    .video-list::-webkit-scrollbar-thumb {
      background: linear-gradient(135deg, #667eea, #764ba2);
      border-radius: 4px;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .video-list::-webkit-scrollbar-thumb:hover {
      background: linear-gradient(135deg, #764ba2, #667eea);
    }

    .video-item {
      display: flex;
      align-items: center;
      padding: 20px;
      margin: 8px 16px;
      border-radius: 16px;
      background: rgba(255, 255, 255, 0.8);
      border: 1px solid rgba(102, 126, 234, 0.1);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
    }

    .video-item::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .video-item:hover {
      background: rgba(255, 255, 255, 0.95);
      border-color: rgba(102, 126, 234, 0.3);
      transform: translateY(-2px);
      box-shadow: 0 8px 32px rgba(102, 126, 234, 0.15);
    }

    .video-item:hover::before {
      opacity: 1;
    }

    .video-thumbnail {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      margin-right: 16px;
      flex-shrink: 0;
      position: relative;
      overflow: hidden;
    }

    .video-thumbnail::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
    }

    .video-thumbnail-icon {
      position: relative;
      z-index: 1;
      color: white;
      font-weight: bold;
    }

    .video-info {
      flex: 1;
      margin-right: 16px;
      position: relative;
      z-index: 1;
    }

    .video-filename {
      font-size: 15px;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 8px;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .video-details {
      display: flex;
      gap: 16px;
      margin-bottom: 4px;
    }

    .video-detail-item {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 4px 8px;
      background: rgba(102, 126, 234, 0.1);
      border-radius: 8px;
      font-size: 12px;
      font-weight: 500;
      color: #667eea;
    }

    .video-detail-item span:first-child {
      font-size: 14px;
    }

    .view-btn {
      background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
      color: white;
      border: none;
      padding: 14px 24px;
      border-radius: 12px;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 4px 16px rgba(76, 175, 80, 0.3);
      min-width: 100px;
      position: relative;
      z-index: 1;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .view-btn:hover {
      background: linear-gradient(135deg, #45a049 0%, #4CAF50 100%);
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
    }

    .view-btn:active {
      transform: translateY(0);
      box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
    }

    .no-videos, .loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 60px 20px;
      text-align: center;
      background: rgba(255, 255, 255, 0.8);
      margin: 20px;
      border-radius: 20px;
      border: 1px solid rgba(102, 126, 234, 0.1);
    }

    .no-videos-icon, .loading-icon {
      font-size: 64px;
      margin-bottom: 20px;
      opacity: 0.6;
      background: linear-gradient(135deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .no-videos-text, .loading-text {
      font-size: 16px;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 8px;
    }

    .no-videos-subtitle {
      font-size: 14px;
      color: #7f8c8d;
      opacity: 0.8;
    }

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba(102, 126, 234, 0.2);
      border-top: 4px solid #667eea;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .stats {
      padding: 12px 20px;
      background: rgba(102, 126, 234, 0.05);
      border-top: 1px solid rgba(102, 126, 234, 0.1);
      text-align: center;
      font-size: 13px;
      color: #667eea;
      font-weight: 500;
    }
  </style>
</head>
<body>
  <div class="header">
    <h2>🎬 Pinterest 视频查看器</h2>
    <div class="filters">
      <div class="filter-wrapper">
        <div class="filter-icon">📺</div>
        <select id="resolution-filter">
          <option value="all">所有分辨率</option>
          <option value="240w">240p</option>
          <option value="360w">360p</option>
          <option value="720w">720p</option>
        </select>
      </div>

      <div class="filter-wrapper">
        <div class="filter-icon">🎞️</div>
        <select id="format-filter">
          <option value="all">所有格式</option>
          <option value="cmfv">CMFV</option>
          <option value="mp4">MP4</option>
        </select>
      </div>
    </div>
  </div>

  <div class="content">
    <div id="video-list" class="video-list">
      <div class="loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在搜索视频...</div>
      </div>
    </div>
    <div id="stats" class="stats" style="display: none;">
      找到 <span id="video-count">0</span> 个视频
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
